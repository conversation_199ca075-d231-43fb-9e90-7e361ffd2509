var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findLastInternal=function($array$$,$callback$$,$thisArg$$){$array$$ instanceof String&&($array$$=String($array$$));for(var $i$$=$array$$.length-1;$i$$>=0;$i$$--){var $value$$=$array$$[$i$$];if($callback$$.call($thisArg$$,$value$$,$i$$,$array$$))return{i:$i$$,v:$value$$}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_ES6=!1;$jscomp.ASSUME_ES2020=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;
$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;$jscomp.INSTRUMENT_ASYNC_CONTEXT=!0;$jscomp.defineProperty=$jscomp.ASSUME_ES5||typeof Object.defineProperties=="function"?Object.defineProperty:function($target$$,$property$$,$descriptor$$){if($target$$==Array.prototype||$target$$==Object.prototype)return $target$$;$target$$[$property$$]=$descriptor$$.value;return $target$$};
$jscomp.getGlobal=function($passedInThis_possibleGlobals$$){$passedInThis_possibleGlobals$$=["object"==typeof globalThis&&globalThis,$passedInThis_possibleGlobals$$,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var $i$$=0;$i$$<$passedInThis_possibleGlobals$$.length;++$i$$){var $maybeGlobal$$=$passedInThis_possibleGlobals$$[$i$$];if($maybeGlobal$$&&$maybeGlobal$$.Math==Math)return $maybeGlobal$$}throw Error("Cannot find global object");};
$jscomp.global=$jscomp.ASSUME_ES2020?globalThis:$jscomp.getGlobal(this);$jscomp.IS_SYMBOL_NATIVE=typeof Symbol==="function"&&typeof Symbol("x")==="symbol";$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";
var $jscomp$lookupPolyfilledValue=function($target$$,$property$$,$isOptionalAccess_obfuscatedName_polyfill$$){if(!$isOptionalAccess_obfuscatedName_polyfill$$||$target$$!=null){$isOptionalAccess_obfuscatedName_polyfill$$=$jscomp.propertyToPolyfillSymbol[$property$$];if($isOptionalAccess_obfuscatedName_polyfill$$==null)return $target$$[$property$$];$isOptionalAccess_obfuscatedName_polyfill$$=$target$$[$isOptionalAccess_obfuscatedName_polyfill$$];return $isOptionalAccess_obfuscatedName_polyfill$$!==
void 0?$isOptionalAccess_obfuscatedName_polyfill$$:$target$$[$property$$]}};$jscomp.polyfill=function($target$$,$polyfill$$,$fromLang$$,$toLang$$){$polyfill$$&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated($target$$,$polyfill$$,$fromLang$$,$toLang$$):$jscomp.polyfillUnisolated($target$$,$polyfill$$,$fromLang$$,$toLang$$))};
$jscomp.polyfillUnisolated=function($property$jscomp$18_split_target$$,$impl_polyfill$$,$fromLang$jscomp$1_obj$$,$i$jscomp$5_orig_toLang$$){$fromLang$jscomp$1_obj$$=$jscomp.global;$property$jscomp$18_split_target$$=$property$jscomp$18_split_target$$.split(".");for($i$jscomp$5_orig_toLang$$=0;$i$jscomp$5_orig_toLang$$<$property$jscomp$18_split_target$$.length-1;$i$jscomp$5_orig_toLang$$++){var $key$$=$property$jscomp$18_split_target$$[$i$jscomp$5_orig_toLang$$];if(!($key$$ in $fromLang$jscomp$1_obj$$))return;
$fromLang$jscomp$1_obj$$=$fromLang$jscomp$1_obj$$[$key$$]}$property$jscomp$18_split_target$$=$property$jscomp$18_split_target$$[$property$jscomp$18_split_target$$.length-1];$i$jscomp$5_orig_toLang$$=$fromLang$jscomp$1_obj$$[$property$jscomp$18_split_target$$];$impl_polyfill$$=$impl_polyfill$$($i$jscomp$5_orig_toLang$$);$impl_polyfill$$!=$i$jscomp$5_orig_toLang$$&&$impl_polyfill$$!=null&&$jscomp.defineProperty($fromLang$jscomp$1_obj$$,$property$jscomp$18_split_target$$,{configurable:!0,writable:!0,
value:$impl_polyfill$$})};
$jscomp.polyfillIsolated=function($isSimpleName_target$$,$impl$jscomp$1_polyfill$$,$BIN_ID_fromLang$$,$ownerObject_root$jscomp$3_toLang$$){var $property$jscomp$19_split$$=$isSimpleName_target$$.split(".");$isSimpleName_target$$=$property$jscomp$19_split$$.length===1;$ownerObject_root$jscomp$3_toLang$$=$property$jscomp$19_split$$[0];$ownerObject_root$jscomp$3_toLang$$=!$isSimpleName_target$$&&$ownerObject_root$jscomp$3_toLang$$ in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var $i$$=0;$i$$<
$property$jscomp$19_split$$.length-1;$i$$++){var $key$$=$property$jscomp$19_split$$[$i$$];if(!($key$$ in $ownerObject_root$jscomp$3_toLang$$))return;$ownerObject_root$jscomp$3_toLang$$=$ownerObject_root$jscomp$3_toLang$$[$key$$]}$property$jscomp$19_split$$=$property$jscomp$19_split$$[$property$jscomp$19_split$$.length-1];$BIN_ID_fromLang$$=$jscomp.IS_SYMBOL_NATIVE&&$BIN_ID_fromLang$$==="es6"?$ownerObject_root$jscomp$3_toLang$$[$property$jscomp$19_split$$]:null;$impl$jscomp$1_polyfill$$=$impl$jscomp$1_polyfill$$($BIN_ID_fromLang$$);
$impl$jscomp$1_polyfill$$!=null&&($isSimpleName_target$$?$jscomp.defineProperty($jscomp.polyfills,$property$jscomp$19_split$$,{configurable:!0,writable:!0,value:$impl$jscomp$1_polyfill$$}):$impl$jscomp$1_polyfill$$!==$BIN_ID_fromLang$$&&($jscomp.propertyToPolyfillSymbol[$property$jscomp$19_split$$]===void 0&&($BIN_ID_fromLang$$=Math.random()*1E9>>>0,$jscomp.propertyToPolyfillSymbol[$property$jscomp$19_split$$]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol($property$jscomp$19_split$$):$jscomp.POLYFILL_PREFIX+
$BIN_ID_fromLang$$+"$"+$property$jscomp$19_split$$),$jscomp.defineProperty($ownerObject_root$jscomp$3_toLang$$,$jscomp.propertyToPolyfillSymbol[$property$jscomp$19_split$$],{configurable:!0,writable:!0,value:$impl$jscomp$1_polyfill$$})))};$jscomp.polyfill("Array.prototype.findLast",function($orig$$){return $orig$$?$orig$$:function($callback$$,$opt_thisArg$$){return $jscomp.findLastInternal(this,$callback$$,$opt_thisArg$$).v}},"es_next","es3");
$jscomp.typedArrayFindLast=function($orig$$){return $orig$$?$orig$$:function($callback$$,$opt_thisArg$$){return $jscomp.findLastInternal(this,$callback$$,$opt_thisArg$$).v}};$jscomp.polyfill("Int8Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Uint8Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Uint8ClampedArray.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");
$jscomp.polyfill("Int16Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Uint16Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Int32Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Uint32Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.polyfill("Float32Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");
$jscomp.polyfill("Float64Array.prototype.findLast",$jscomp.typedArrayFindLast,"es_next","es5");$jscomp.owns=function($obj$$,$prop$$){return Object.prototype.hasOwnProperty.call($obj$$,$prop$$)};$jscomp.polyfill("Object.entries",function($orig$$){return $orig$$?$orig$$:function($obj$$){var $result$$=[],$key$$;for($key$$ in $obj$$)$jscomp.owns($obj$$,$key$$)&&$result$$.push([$key$$,$obj$$[$key$$]]);return $result$$}},"es8","es3");
Ext.define("MyAppName.Application",{extend:"Ext.app.Application",name:"MyAppName",quickTips:!1,platformConfig:{desktop:{quickTips:!0}},launch:function(){var $object_property$$=new WeakMap;const $key$$=Symbol("my ref");$object_property$$.set($key$$,{a:1});console.log($object_property$$.get($key$$));console.log([{a:1,b:1},{a:2,b:2},{a:3,b:3},{a:4,b:4}].findLast($n$$=>$n$$));"igor\ud800 igor\ud800komolov \udc00yourfuse your\udc00fuse yourFuse emoji\ud83d\ude00".split(" ").forEach($str$$=>{console.log(`Processed String: ${$str$$.toWellFormed()}`)});
$object_property$$=new Int32Array(new SharedArrayBuffer(1024));Atomics.waitSync($object_property$$,0,0);$object_property$$[0]=123;Atomics.notify($object_property$$,0,1);$object_property$$={one:1,two:2};Object.entries($object_property$$).forEach(([$key$$,$value$$])=>{console.log(`key: ${$key$$}, value: ${$value$$}`)});Object.entries($object_property$$).forEach($key$$=>{console.log(`key: ${$key$$}, value: ${value}`)});$object_property$$=($object_property$$={myProperty:"hello"},$object_property$$.myProperty)?
$object_property$$.myProperty.big():void 0;console.log($object_property$$)}});
